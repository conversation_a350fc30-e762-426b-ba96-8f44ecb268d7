name: SonarCloud Scan
on:
  push:
    branches:
      - dev
  pull_request:
    types: [opened, synchronize, reopened]
jobs:
  sonarcloud:
    name: SonarCloud
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUBTOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
